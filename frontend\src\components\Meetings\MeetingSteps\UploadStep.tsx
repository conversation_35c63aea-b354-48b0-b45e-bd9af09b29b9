import React, { useState, useEffect } from 'react'; // Added useEffect
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import * as tus from 'tus-js-client';
import { Session, User } from '@supabase/supabase-js'; // Import Session and User
import { MeetingState, AssociatedEntity } from '../../../types/meeting.types';
import Modal from '../../UI/Modal';
import { useToastContext } from '../../../hooks/useToastContext';
import FileUploadProgress from '../../UI/FileUploadProgress';
import FileDropzone from '../../UI/FileDropzone';
import {
  EmpresaApiResponse
} from '../../CRM/EmpresaCreateForm'; // Assuming these export API response types
import {
  PersonaApiResponse
} from '../../CRM/PersonaCreateForm';

import EmpresaCreateForm from '../../CRM/EmpresaCreateForm';
import PersonaCreateForm from '../../CRM/PersonaCreateForm';

import SelectExistingEntityModal from '../SelectExistingEntityModal'; // Assuming this path is correct relative to MeetingProcessingForm

// Validation schema
const uploadSchema = z.object({
  file: z
    .any()
    .refine((files) => {
      // Handle both FileList and our custom FileList-like object
      if (!files) return true; // Allow empty for editing existing meetings
      if (files instanceof FileList) return files.length <= 1;
      if (typeof files === 'object' && files.length !== undefined) return files.length <= 1;
      return false;
    }, "Se requiere un archivo de grabación o ninguno si se edita.")
    .refine((files) => {
      if (!files || files.length === 0) return true; // Allow empty for editing
      const file = files instanceof FileList ? files[0] : files[0];
      if (!file) return true;
      return file.type.startsWith('audio/') || file.type.startsWith('video/');
    }, "El archivo debe ser de audio o video.")
    .refine((files) => {
      if (!files || files.length === 0) return true; // Allow empty for editing
      const file = files instanceof FileList ? files[0] : files[0];
      if (!file) return true;
      return file.size <= 500 * 1024 * 1024; // 500MB
    }, "El archivo no puede ser mayor a 500MB."),
  titulo: z.string().optional(),
  observaciones_iniciales: z.string().optional(),
  fecha_reunion: z.string().optional(),
});

type UploadFormValues = z.infer<typeof uploadSchema>;

interface UploadStepProps {
  meetingState: MeetingState; // Changed from 'state' to 'meetingState' for clarity
  selectedFile: File | null;
  setSelectedFile: (file: File | null) => void;
  onAddAssociatedEntity: (entity: AssociatedEntity) => void;
  onRemoveAssociatedEntity: (id: string, tipo: AssociatedEntity['tipo']) => void;
  onSetUploadProgress: (progress: number) => void;
  onSetUploading: (isUploading: boolean) => void;
  onSetMeetingId: (id: string) => void;
  onSetCurrentStep: (step: number) => void;
  session: Session | null;
  user: User | null;
}

const UploadStep: React.FC<UploadStepProps> = ({
  meetingState,
  selectedFile,
  setSelectedFile,
  onAddAssociatedEntity,
  onRemoveAssociatedEntity,
  onSetUploadProgress,
  onSetUploading,
  onSetMeetingId,
  onSetCurrentStep,
  session,
  user,
}) => {
  // Toast notifications
  const { success, error: showError, info } = useToastContext();

  // Helper function to get error message
  const getErrorMessage = (error: unknown): string => {
    if (typeof error === 'string') return error;
    if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string') {
      return error.message;
    }
    return 'Error en el campo';
  };

  // Modal states
  const [activeCrmModal, setActiveCrmModal] = useState<'empresa' | 'persona' | null>(null);
  const [activeSelectModal, setActiveSelectModal] = useState<'empresa' | 'persona' | null>(null);

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting }, // isSubmitting from RHF can be used
    watch,
    // reset, // Removed as unused
    setValue, // Added setValue
  } = useForm<UploadFormValues>({
    resolver: zodResolver(uploadSchema),
    // Default values will be set by useEffect based on meetingState
  });

  // Populate form with existing data from meetingState
  useEffect(() => {
    if (meetingState.id) { // If editing an existing meeting
      setValue('titulo', meetingState.titulo || '');
      setValue('observaciones_iniciales', meetingState.observacionesIniciales || '');
      setValue('fecha_reunion', meetingState.fechaReunion ? new Date(meetingState.fechaReunion).toISOString().slice(0, 16) : new Date().toISOString().slice(0, 16));
      // File input cannot be reset or pre-filled programmatically for security reasons.
      // It will show "No file chosen" or the name of the file if the user selects one.
    } else {
        // For new meetings, set default or clear
        setValue('titulo', '');
        setValue('observaciones_iniciales', '');
        setValue('fecha_reunion', new Date().toISOString().slice(0, 16));
    }
  }, [meetingState.id, meetingState.titulo, meetingState.observacionesIniciales, meetingState.fechaReunion, setValue]);

  // Watch file input
  const watchedFile = watch("file");
  useEffect(() => {
    if (watchedFile && watchedFile.length > 0) {
      setSelectedFile(watchedFile[0]);
    } else if (!meetingState.id) { // Only clear if it's not an existing meeting without a new file selected
      setSelectedFile(null);
    }
  }, [watchedFile, setSelectedFile, meetingState.id]);

  // Handle entity selection
  const handleExistingEntitySelected = (entities: AssociatedEntity[]) => {
    entities.forEach(entity => onAddAssociatedEntity(entity));
    setActiveSelectModal(null);
  };

  // Handle entity creation
  const handleCrmEntityCreated = (
    entity: EmpresaApiResponse | PersonaApiResponse,
    type: AssociatedEntity['tipo']
  ) => {
    let entityName = 'Nombre Desconocido';
    // Type guard to ensure 'nombre' property exists
    if (entity && typeof entity === 'object' && 'nombre' in entity && typeof entity.nombre === 'string') {
        entityName = entity.nombre;
    }

    // Type guard for 'apellidos' (specific to Persona)
    if (type === 'persona' && entity && typeof entity === 'object' && 'apellidos' in entity && entity.apellidos && typeof entity.apellidos === 'string') {
      entityName = `${entityName} ${entity.apellidos}`;
    }

    onAddAssociatedEntity({
      id: entity.id,
      nombre: entityName.trim(),
      tipo: type,
    });

    setActiveCrmModal(null);
  };

  // Form submission
  const onSubmit: SubmitHandler<UploadFormValues> = async (data) => {
    try {
      // If editing existing meeting with pending speaker assignment, just go to step 2
      if (meetingState.id && meetingState.status === 'pending_asignacion_speakers') {
        onSetCurrentStep(2);
        return;
      }

      // Validate file for new meetings
      if (!selectedFile && !meetingState.id) {
        showError("Archivo requerido", "Por favor, seleccione un archivo de grabación.");
        return;
      }

      if (!session?.access_token) {
        showError("Error de autenticación", "Por favor, inicie sesión de nuevo.");
        return;
      }

      // Validate file size (max 500MB)
      if (selectedFile && selectedFile.size > 500 * 1024 * 1024) {
        showError("Archivo demasiado grande", "El tamaño máximo permitido es 500MB.");
        return;
      }

      // Validate file type
      if (selectedFile && !selectedFile.type.startsWith('audio/') && !selectedFile.type.startsWith('video/')) {
        showError("Tipo de archivo inválido", "Por favor, seleccione un archivo de audio o video válido.");
        return;
      }

    // Only upload if it's a new meeting
    if (!meetingState.id && selectedFile) {
      onSetUploading(true);
      onSetUploadProgress(0);

      const fileToUpload = selectedFile; // Ensure this is the correct file
      // Simplified for debugging "Invalid key" error
      const simpleFileNameForTest = `test_upload_${new Date().getTime()}.${fileToUpload.name.split('.').pop()}`;
      const fileName = user?.id ? `user_${user.id}/${simpleFileNameForTest}` : `unknown_user/${simpleFileNameForTest}`;
      console.log('[UploadStep] Constructed objectPath for TUS:', `reuniones/${fileName}`); // Log the path
      const bucketName = 'almacenamiento'; // As defined in backend
      const objectPath = `reuniones/${fileName}`; // Matches backend path construction

      const upload = new tus.Upload(fileToUpload, {
        endpoint: `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/upload/resumable`,
        retryDelays: [0, 3000, 5000, 10000, 20000],
        headers: {
          authorization: `Bearer ${session.access_token}`,
          'x-upsert': 'false', // Set to 'true' if you want to overwrite
        },
        metadata: {
          bucketName: bucketName,
          objectName: objectPath, // This is the full path within the bucket
          contentType: fileToUpload.type,
          cacheControl: '3600',
        },
        chunkSize: 6 * 1024 * 1024, // 6MB as recommended by Supabase docs
        uploadDataDuringCreation: true,
        removeFingerprintOnSuccess: true,
        onError: (error) => {
          console.error('TUS Upload Failed:', error);
          let errorMessage = 'Error desconocido al subir el archivo';

          if (error instanceof Error) {
            errorMessage = error.message;
          } else if (typeof error === 'string') {
            errorMessage = error;
          }

          // Provide more specific error messages
          if (errorMessage.includes('413')) {
            errorMessage = 'El archivo es demasiado grande para el servidor';
          } else if (errorMessage.includes('401') || errorMessage.includes('403')) {
            errorMessage = 'Error de autenticación. Por favor, inicie sesión de nuevo';
          } else if (errorMessage.includes('network') || errorMessage.includes('Network')) {
            errorMessage = 'Error de conexión. Verifique su conexión a internet';
          }

          showError("Error de subida", errorMessage);
          onSetUploading(false);
          onSetUploadProgress(0);
        },
        onProgress: (bytesUploaded, bytesTotal) => {
          const percentage = ((bytesUploaded / bytesTotal) * 100).toFixed(2);
          onSetUploadProgress(parseFloat(percentage));
          console.log('TUS Progress:', bytesUploaded, bytesTotal, percentage + '%');
        },
        onSuccess: async () => {
          console.log('TUS Upload Succeeded:', upload.url);
          onSetUploadProgress(100);
          // onSetUploading(false); // Backend call will set this

          const publicURL = `${import.meta.env.VITE_SUPABASE_URL}/storage/v1/object/public/${bucketName}/${objectPath}`;

          const backendPayload = {
            ...data, // Includes titulo, observaciones_iniciales, fecha_reunion from RHF
            fecha_reunion: data.fecha_reunion ? new Date(data.fecha_reunion).toISOString() : null,
            url_grabacion_publica: publicURL,
            file_storage_path: objectPath,
            empresas_asociadas_ids: meetingState.associatedEntities.filter(e => e.tipo === 'empresa').map(e => e.id),
            personas_asociadas_ids: meetingState.associatedEntities.filter(e => e.tipo === 'persona').map(e => e.id),
          };

          try {
            const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.access_token}`,
              },
              body: JSON.stringify(backendPayload),
            });

            if (!response.ok) {
              const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
              throw new Error(errorData.detail || `Error al crear la reunión en backend: ${response.statusText}`);
            }
            const result = await response.json();
            console.log("Reunión creada en backend:", result);

            onSetMeetingId(result.id); // This will trigger Realtime connection via useMeetingProcessing hook
            // The hook will then update status and currentStep based on Realtime/polling
            onSetCurrentStep(1.5); // Go to loading step for transcription
            success("Archivo subido", "Reunión enviada para procesamiento.");

          } catch (error) {
            console.error("Error submitting Step 1 to backend:", error);
            showError("Error del servidor", error instanceof Error ? error.message : 'Error desconocido');
            // Consider if we need to delete the uploaded file from storage if backend call fails
          } finally {
            onSetUploading(false);
          }
        },
      });
      upload.start();
    } else if (meetingState.id) {
      // Logic for updating an existing meeting's metadata (if file is not changed)
      // This might involve a different backend endpoint or logic.
      // For now, if it's an existing meeting, we assume the primary action is to proceed if status allows.
      console.log("Updating existing meeting metadata (not implemented yet, proceeding to next step if applicable)");
      if (meetingState.status === 'pending_asignacion_speakers') {
        onSetCurrentStep(2);
      } else {
        // Potentially save metadata changes here if the form was editable for an existing meeting
        info("Metadata guardada", "El estado actual no permite avanzar a la asignación de speakers.");
      }
    }
    } catch (error) {
      console.error("Error in form submission:", error);
      showError("Error en el formulario", error instanceof Error ? error.message : 'Error desconocido');
      onSetUploading(false);
    }
  };

  // --- Common Form Styling ---
  const formFieldClass = "mb-4";
  const labelClass = "block text-sm font-medium text-gray-700 mb-1";
  const inputClass = "mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm disabled:bg-gray-100";
  const errorClass = "mt-1 text-xs text-red-600";
  const buttonClass = "px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50";

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 bg-white p-6 shadow-md rounded-lg">
      <h2 className="text-xl font-semibold text-gray-700 mb-4">Paso 1: Cargar Grabación y Asociar Entidades</h2>

      <div className={formFieldClass}>
        <label className={labelClass}>Archivo de Grabación (audio/video)*</label>
        {!meetingState.id ? (
          <FileDropzone
            onFileSelect={(file) => {
              setSelectedFile(file);
              // Create a more compatible FileList-like object
              const dt = new DataTransfer();
              dt.items.add(file);
              const fileList = dt.files;
              setValue('file', fileList);
            }}
            currentFile={selectedFile}
            disabled={meetingState.isUploading}
            acceptedTypes={['audio/*', 'video/*']}
            maxSize={500 * 1024 * 1024} // 500MB
          />
        ) : (
          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <p className="text-sm text-gray-600">Reunión existente. Para cambiar el archivo, crea una nueva reunión.</p>
          </div>
        )}
        {errors.file && <p className={errorClass}>{getErrorMessage(errors.file)}</p>}
      </div>

      {meetingState.id && meetingState.status && (
        <div className="my-4 p-3 bg-yellow-50 border border-yellow-300 rounded-md text-sm text-yellow-700">
          Estado actual: <strong>{meetingState.status}</strong>
          {(meetingState.status === 'pending_transcripcion' || meetingState.status === 'procesando_ia') &&
            <p>Esperando la finalización del proceso automático. La página se actualizará cuando esté lista.</p>
          }
        </div>
      )}

      <div className={formFieldClass}>
        <label htmlFor="titulo" className={labelClass}>Título (Opcional)</label>
        <input id="titulo" {...register("titulo")} className={inputClass} disabled={meetingState.isUploading} />
        {errors.titulo && <p className={errorClass}>{errors.titulo.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="fecha_reunion" className={labelClass}>Fecha y Hora de Reunión (Opcional)</label>
        <input
          type="datetime-local"
          id="fecha_reunion"
          {...register("fecha_reunion")}
          className={inputClass}
          disabled={meetingState.isUploading}
        />
        {errors.fecha_reunion && <p className={errorClass}>{errors.fecha_reunion.message}</p>}
      </div>

      <div className={formFieldClass}>
        <label htmlFor="observaciones_iniciales" className={labelClass}>Observaciones Iniciales (Opcional)</label>
        <textarea id="observaciones_iniciales" {...register("observaciones_iniciales")} rows={3} className={inputClass} disabled={meetingState.isUploading}></textarea>
        {errors.observaciones_iniciales && <p className={errorClass}>{errors.observaciones_iniciales.message}</p>}
      </div>

      <div className="border-t pt-4 mt-6">
        <h3 className="text-lg font-medium text-gray-700 mb-1">Asociar Entidades CRM</h3>
        <p className="text-xs text-gray-500 mb-4">Puede crear nuevas entidades o seleccionar existentes para vincularlas a esta reunión.</p>

        <div className="space-y-3">
          {[
            { type: 'empresa', label: 'Empresas', selectModal: () => setActiveSelectModal('empresa'), addModal: () => setActiveCrmModal('empresa'), addLabel: '+ Nueva Empresa' },
            { type: 'persona', label: 'Personas', selectModal: () => setActiveSelectModal('persona'), addModal: () => setActiveCrmModal('persona'), addLabel: '+ Nueva Persona' },
          ].map(entityItem => (
            <div key={entityItem.type} className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200">
              <span className="text-sm font-medium text-gray-700">{entityItem.label}</span>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={entityItem.selectModal}
                  className="px-3 py-1.5 text-xs font-medium text-indigo-700 bg-indigo-100 rounded-md hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-indigo-500"
                  disabled={meetingState.isUploading}
                >
                  Seleccionar Existente
                </button>
                <button
                  type="button"
                  onClick={entityItem.addModal}
                  className="px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-green-500"
                  disabled={meetingState.isUploading}
                >
                  {entityItem.addLabel}
                </button>
              </div>
            </div>
          ))}
        </div>

        {meetingState.associatedEntities.length > 0 && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Entidades Asociadas:</h4>
            <div className="flex flex-wrap gap-2">
              {meetingState.associatedEntities.map(e => (
                <div key={`${e.tipo}-${e.id}`} className="flex items-center bg-indigo-100 text-indigo-700 text-xs font-semibold px-2.5 py-1 rounded-full">
                  <span>{e.nombre} <span className="font-normal opacity-75">({e.tipo})</span></span>
                  <button
                    type="button"
                    onClick={() => onRemoveAssociatedEntity(e.id, e.tipo)}
                    className="ml-2 text-indigo-500 hover:text-indigo-700 focus:outline-none"
                    aria-label={`Quitar ${e.nombre}`}
                    disabled={meetingState.isUploading}
                  >
                    &times;
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end pt-4">
        {(!meetingState.id || meetingState.status === 'pending_asignacion_speakers') && (
          <button
            type="submit"
            className={buttonClass}
            disabled={meetingState.isUploading || isSubmitting || (!!meetingState.id && meetingState.status !== null && meetingState.status !== 'pending_asignacion_speakers')}
          >
            {meetingState.isUploading ? `Subiendo (${meetingState.uploadProgress.toFixed(0)}%)...` :
             (isSubmitting ? 'Procesando...' :
              (meetingState.id && meetingState.status === 'pending_asignacion_speakers' ? 'Ir a Asignar Speakers' : 'Siguiente: Asignar Participantes'))}
          </button>
        )}
      </div>

      {/* File Upload Progress */}
      {meetingState.isUploading && selectedFile && (
        <div className="mt-6">
          <FileUploadProgress
            fileName={selectedFile.name}
            progress={meetingState.uploadProgress}
            isUploading={meetingState.isUploading}
            isComplete={false}
            hasError={false}
            fileSize={selectedFile.size}
          />
        </div>
      )}

      {/* --- CRM Modals --- */}
      <Modal isOpen={activeCrmModal === 'empresa'} onClose={() => setActiveCrmModal(null)} title="Crear Nueva Empresa" size="lg">
        <EmpresaCreateForm onSubmitSuccess={(data) => handleCrmEntityCreated(data, 'empresa')} onCancel={() => setActiveCrmModal(null)} />
      </Modal>
      <Modal isOpen={activeCrmModal === 'persona'} onClose={() => setActiveCrmModal(null)} title="Crear Nueva Persona" size="lg">
        <PersonaCreateForm onSubmitSuccess={(data) => handleCrmEntityCreated(data, 'persona')} onCancel={() => setActiveCrmModal(null)} />
      </Modal>


      <SelectExistingEntityModal
        isOpen={!!activeSelectModal}
        onClose={() => setActiveSelectModal(null)}
        entityType={activeSelectModal}
        onEntitiesSelected={handleExistingEntitySelected}
        alreadyAssociatedIds={meetingState.associatedEntities.map(ae => ae.id)}
      />
    </form>
  );
};

export default UploadStep;