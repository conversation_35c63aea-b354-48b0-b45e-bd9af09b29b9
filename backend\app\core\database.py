from supabase import create_client, AsyncClient
from .config import settings
import logging
import asyncpg
from typing import Optional

logger = logging.getLogger(__name__)

# Supabase client
supabase_client: AsyncClient | None = None

# AsyncPG connection pools for direct database access
db_pool_readonly: Optional[asyncpg.Pool] = None
db_pool_readwrite: Optional[asyncpg.Pool] = None

async def get_supabase_client() -> AsyncClient:
    """
    Returns the initialized Supabase async client instance.
    Raises an exception if initialization failed.
    """
    global supabase_client
    if supabase_client is None:
        logger.error("Supabase client has not been initialized. Call init_supabase first.")
        raise RuntimeError("Supabase client not initialized")
    return supabase_client

async def init_supabase():
    """
    Initializes the Supabase async client using credentials from settings.
    This should be called during application startup.
    """
    global supabase_client
    if supabase_client is not None:
        logger.warning("Supabase client already initialized.")
        return

    try:
        url: str = settings.SUPABASE_URL
        key: str = settings.SUPABASE_SERVICE_ROLE_KEY.get_secret_value() # Use service role key for backend operations

        if not url or not key or "YOUR_SUPABASE" in url or "YOUR_SUPABASE" in key:
             logger.error("Supabase URL or Service Role Key is not configured correctly in settings.")
             raise ValueError("Supabase URL or Service Role Key not configured.")

        # Define longer timeouts for httpx client, especially for file uploads
        # Default is 5 seconds for all operations.
        # We can set connect, read, write, and pool timeouts.
        # For large file uploads, 'read' (reading response from server after upload) and 'write' (sending data) are important.
        # Let's set a general read timeout of 300 seconds.
        # Note: `httpx_client_options` is the correct parameter name for `create_client`
        # to pass options to the underlying httpx.AsyncClient.
        # The `postgrest_client_options` is for the PostgrestClient, not general HTTP.
        # However, `supabase-py`'s `create_client` doesn't directly expose `httpx_client_options`.
        # Instead, the AsyncClient it creates uses default httpx timeouts.
        # To modify timeouts, we would typically pass a pre-configured httpx.AsyncClient
        # to `AsyncClient(..., session=custom_httpx_client)`.
        # But `create_client` doesn't allow passing a custom session directly.

        # Let's check the `supabase-py` documentation again.
        # The `create_client` function itself does not take httpx options.
        # The `AsyncClient` instance has a `postgrest: AsyncPostgrestClient` attribute.
        # The `AsyncPostgrestClient` is initialized with an `httpx.AsyncClient` session.
        # We might need to modify the session *after* client creation if direct pass-through isn't available.

        # Looking at supabase-py source for `create_client` and `AsyncClient`:
        # `AsyncClient` constructor takes `httpx_session: Optional[httpx.AsyncClient] = None`.
        # `create_client` does: `return Client(..., rest_client=AsyncPostgrestClient, storage_client=AsyncStorageClient, realtime_client=AsyncRealtimeClient)`
        # and `Client` passes options down.
        # It seems `postgrest_client_options={"session": custom_session}` might be the way,
        # or if there's a general `http_options` or similar.

        # Simpler approach: The `AsyncClient`'s `storage` attribute is an `AsyncStorageClient`.
        # This client also uses an `httpx.AsyncClient`.
        # The `supabase.create_client` function has `options: ClientOptions` which includes `http_timeout`.
        # Configure custom httpx.AsyncClient for storage with specific timeouts.
        import httpx
        from supabase.lib.client_options import ClientOptions

        # 1. Define custom timeout settings for httpx
        # Connect timeout: 5s, Read timeout: 300s, Write timeout: 300s, Pool timeout: 5s
        custom_httpx_timeout = httpx.Timeout(5.0, read=300.0, write=300.0, pool=5.0)

        # 2. Create a custom httpx.AsyncClient for storage operations
        storage_http_session = httpx.AsyncClient(timeout=custom_httpx_timeout) # This session is not directly used by create_client options in this way.
                                                                            # Instead, ClientOptions takes timeout objects directly for its sub-clients.

        # 3. Prepare ClientOptions, passing the custom timeout directly to storage_client_timeout
        # As hinted by the error: "Did you mean 'storage_client_timeout'?"
        # This parameter expects an httpx.Timeout object or a float.
        options = ClientOptions(
            storage_client_timeout=custom_httpx_timeout
            # We could also set postgrest_client_timeout if needed:
            # postgrest_client_timeout=httpx.Timeout(5.0, read=60.0)
        )

        supabase_client = create_client(url, key, options=options)
        logger.info("Supabase client initialized successfully with custom timeouts for storage client.")

        # Optional: Test connection (e.g., fetch a small amount of data)
        # try:
        #     await supabase_client.table("agentes").select("id").limit(1).execute()
        #     logger.info("Supabase connection test successful.")
        # except Exception as e:
        #     logger.error(f"Supabase connection test failed: {e}")
        #     # Decide if you want to raise an error here or just log it

    except Exception as e:
        logger.exception(f"Failed to initialize Supabase client: {e}")
        # Depending on your application's needs, you might want to exit or handle this differently.
        raise RuntimeError(f"Failed to initialize Supabase client: {e}") from e

async def close_supabase_client():
    """
    Closes the Supabase client session.
    This should be called during application shutdown.
    """
    global supabase_client
    if supabase_client:
        try:
            # The supabase-py client manages its internal httpx sessions.
            # We only need to call aclose() on the main client's components that use httpx.
            # The AsyncClient itself doesn't have a direct aclose().
            # We need to close the sessions of its sub-clients like postgrest and storage.

            # Close Postgrest session
            if (supabase_client.postgrest is not None and
                hasattr(supabase_client.postgrest, 'aclose') and
                callable(getattr(supabase_client.postgrest, 'aclose', None))):
                await supabase_client.postgrest.aclose()
                logger.info("Supabase Postgrest client session closed.")

            # Close Storage session
            # The storage client in supabase-py (AsyncStorageClient) also has an `aclose` method
            # which closes its underlying httpx session.
            if (supabase_client.storage is not None and
                hasattr(supabase_client.storage, 'aclose') and
                callable(getattr(supabase_client.storage, 'aclose', None))):
                await supabase_client.storage.aclose()
                logger.info("Supabase Storage client session closed.")

            # Realtime client (AsyncRealtimeClient) manages WebSocket, not httpx session for REST.
            # It has a disconnect() method if needed, but not typically part of this HTTP session closing.

            supabase_client = None
        except Exception as e:
            logger.exception(f"Error closing Supabase client sub-sessions: {e}")

# AsyncPG Pool Management Functions

async def init_db_pools():
    """
    Initialize AsyncPG connection pools for direct database access.
    This should be called during application startup.
    """
    global db_pool_readonly, db_pool_readwrite

    if db_pool_readonly is not None or db_pool_readwrite is not None:
        logger.warning("Database pools already initialized.")
        return

    try:
        # Build connection DSN for read-write pool
        readwrite_dsn = f"postgresql://{settings.DB_USER}:{settings.DB_PASSWORD.get_secret_value()}@{settings.DB_HOST}:{settings.DB_PORT}/{settings.DB_NAME}"

        # Build connection DSN for read-only pool (use replica if available, otherwise same as read-write)
        readonly_host = settings.DB_REPLICA_HOST if settings.DB_REPLICA_HOST else settings.DB_HOST
        readonly_dsn = f"postgresql://{settings.DB_USER}:{settings.DB_PASSWORD.get_secret_value()}@{readonly_host}:{settings.DB_PORT}/{settings.DB_NAME}"

        # Create read-write pool
        db_pool_readwrite = await asyncpg.create_pool(
            readwrite_dsn,
            min_size=2,
            max_size=10,
            command_timeout=60,
            server_settings={
                'application_name': 'aceleralia_backend_readwrite'
            }
        )
        logger.info("AsyncPG read-write pool initialized successfully.")

        # Create read-only pool
        db_pool_readonly = await asyncpg.create_pool(
            readonly_dsn,
            min_size=2,
            max_size=20,  # More connections for read-only operations
            command_timeout=60,
            server_settings={
                'application_name': 'aceleralia_backend_readonly',
                'default_transaction_read_only': 'on'  # Enforce read-only at connection level
            }
        )
        logger.info("AsyncPG read-only pool initialized successfully.")

    except Exception as e:
        logger.exception(f"Failed to initialize database pools: {e}")
        raise RuntimeError(f"Failed to initialize database pools: {e}") from e

async def get_db_pool_readonly() -> asyncpg.Pool:
    """
    Returns the read-only database pool.
    Raises an exception if not initialized.
    """
    global db_pool_readonly
    if db_pool_readonly is None:
        logger.error("Read-only database pool has not been initialized. Call init_db_pools first.")
        raise RuntimeError("Read-only database pool not initialized")
    return db_pool_readonly

async def get_db_pool_readwrite() -> asyncpg.Pool:
    """
    Returns the read-write database pool.
    Raises an exception if not initialized.
    """
    global db_pool_readwrite
    if db_pool_readwrite is None:
        logger.error("Read-write database pool has not been initialized. Call init_db_pools first.")
        raise RuntimeError("Read-write database pool not initialized")
    return db_pool_readwrite

async def close_db_pools():
    """
    Closes the AsyncPG connection pools.
    This should be called during application shutdown.
    """
    global db_pool_readonly, db_pool_readwrite

    try:
        if db_pool_readonly:
            await db_pool_readonly.close()
            db_pool_readonly = None
            logger.info("Read-only database pool closed.")

        if db_pool_readwrite:
            await db_pool_readwrite.close()
            db_pool_readwrite = None
            logger.info("Read-write database pool closed.")

    except Exception as e:
        logger.exception(f"Error closing database pools: {e}")
