import React, { useState, useEffect, useRef } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth'; // Import useAuth
import { PreguntaPendiente } from '../types/meeting.types';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  SpeakerWaveIcon,
  PlayIcon,
  PauseIcon,
  UserGroupIcon,
  BuildingOfficeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CheckCircleIcon,
  QuestionMarkCircleIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import TranscriptViewer from '../components/Meetings/TranscriptViewer';

// This type should align with the backend's ReunionResponse model
// and include all details for a single meeting.
interface ReunionDetails {
  id: string;
  user_id: string;
  titulo?: string | null;
  observaciones_iniciales?: string | null;
  url_grabacion_original?: string | null;
  url_grabacion_publica?: string | null;
  fecha_reunion?: string | null; // ISO string
  transcripcion_raw?: string | null;
  transcripcion_final?: string | null;
  resumen?: string | null;
  puntos_clave?: Record<string, unknown> | string | null; // JSONB or string for backward compatibility
  preguntas_pendientes?: PreguntaPendiente[] | string | null; // JSONB array or string for backward compatibility
  estado_procesamiento?: string | null;
  info_adicional?: string | null;
  created_at: string; // ISO string
  updated_at: string; // ISO string
  // Associated entities and speaker assignments
  empresas_asociadas?: { id: string, nombre: string }[];
  personas_asociadas?: { id: string, nombre: string }[];

  speaker_asignaciones?: { speaker_tag: string, nombre_asignado: string, tipo_entidad: string, entidad_id: string }[];
}

const MeetingDetailsView: React.FC = () => {
  const { reunionId } = useParams<{ reunionId: string }>();
  const [meeting, setMeeting] = useState<ReunionDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { session } = useAuth();

  // UI state for collapsible sections
  const [isTranscriptExpanded, setIsTranscriptExpanded] = useState<boolean>(false);
  const [isEntitiesExpanded, setIsEntitiesExpanded] = useState<boolean>(true);
  const [isSpeakersExpanded, setIsSpeakersExpanded] = useState<boolean>(true);
  const [isPuntosClaveExpanded, setIsPuntosClaveExpanded] = useState<boolean>(true);
  const [isPreguntasExpanded, setIsPreguntasExpanded] = useState<boolean>(true);

  // Audio player state
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  // Audio player functions
  const toggleAudioPlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
  };

  useEffect(() => {
    const fetchMeetingDetails = async () => {
      if (!reunionId) {
        setError("ID de reunión no encontrado.");
        setIsLoading(false);
        return;
      }
      if (!session?.access_token) {
        setError("No hay sesión de usuario activa o token no disponible.");
        setIsLoading(false);
        setMeeting(null);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/reuniones/${reunionId}`, {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
          },
        });
        if (!response.ok) {
          if (response.status === 404) throw new Error("Reunión no encontrada.");
          const errorData = await response.json().catch(() => ({ detail: `HTTP error ${response.status}` }));
          throw new Error(errorData.detail || `Error fetching meeting details: ${response.statusText}`);
        }
        const data: ReunionDetails = await response.json();

        // For testing purposes, add sample data if not present
        if (data.estado_procesamiento === 'completado' && !data.preguntas_pendientes) {
          // This is just for testing - remove in production
          data.preguntas_pendientes = [
            {
              id_persona: data.personas_asociadas?.[0]?.id || null,
              id_lead_contacto: null,
              pregunta_texto: "¿Podrías proporcionar más detalles sobre el proceso actual de gestión de inventario?",
              info_adicional: "Referencia: Transcripción 15:30-16:45. Mencionado durante la discusión sobre optimización de procesos."
            },

          ];
        }

        setMeeting(data);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Ocurrió un error desconocido.');
        console.error(`Failed to fetch meeting ${reunionId}:`, err);
        setMeeting(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMeetingDetails();
  }, [reunionId, session]);

  if (!session && !isLoading) { // If no session and not loading, show login prompt or similar
    return (
      <div className="p-6 text-center">
        <p>Por favor, <Link to="/login" className="text-indigo-600 hover:text-indigo-800">inicie sesión</Link> para ver los detalles de la reunión.</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-4xl mx-auto">
          <LoadingSpinner
            message="Cargando detalles de la reunión..."
            subMessage="Por favor espere mientras obtenemos la información"
          />
        </div>
      </div>
    );
  }

  if (error) {
    return <div className="p-6 text-center text-red-600">Error: {error}</div>;
  }

  if (!meeting) {
    return <div className="p-6 text-center">Reunión no encontrada.</div>;
  }

  const renderDetailItem = (label: string, value: string | number | null | undefined) => (
    value ? <div className="mb-2"><strong className="font-semibold text-gray-700">{label}:</strong> <span className="text-gray-600">{value}</span></div> : null
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-6">
          <Link
            to="/meetings"
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-800 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
            <span>Volver a la lista de reuniones</span>
          </Link>
        </div>
      <div className="bg-white shadow-xl rounded-lg p-6 md:p-8">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">{meeting.titulo || 'Reunión sin título'}</h1>
        <p className="text-sm text-gray-500 mb-6">
          ID: {meeting.id} | Estado:
          <span className={`ml-1 px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${
            meeting.estado_procesamiento === 'completado' ? 'bg-green-100 text-green-800' :
            meeting.estado_procesamiento === 'pending_transcripcion' ? 'bg-yellow-100 text-yellow-800' :
            meeting.estado_procesamiento === 'pending_asignacion_speakers' ? 'bg-blue-100 text-blue-800' :
            meeting.estado_procesamiento === 'procesando_ia' ? 'bg-purple-100 text-purple-800' :
            'bg-gray-100 text-gray-800'
          }`}>
            {meeting.estado_procesamiento || 'Desconocido'}
          </span>
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 mb-6">
          {renderDetailItem("Fecha de Reunión", meeting.fecha_reunion ? new Date(meeting.fecha_reunion).toLocaleString() : 'N/A')}
          {renderDetailItem("Fecha de Carga", new Date(meeting.created_at).toLocaleString())}
          {renderDetailItem("Última Actualización", new Date(meeting.updated_at).toLocaleString())}
        </div>

        {/* Audio Player */}
        {meeting.url_grabacion_publica && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-3 flex items-center space-x-2">
              <SpeakerWaveIcon className="h-6 w-6" />
              <span>Grabación de la Reunión</span>
            </h2>
            <div className="bg-gray-50 p-4 rounded-lg border">
              <div className="flex items-center space-x-4">
                <button
                  onClick={toggleAudioPlayback}
                  className="flex items-center justify-center w-12 h-12 bg-indigo-600 hover:bg-indigo-700 text-white rounded-full transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="h-6 w-6" />
                  ) : (
                    <PlayIcon className="h-6 w-6" />
                  )}
                </button>
                <div className="flex-1">
                  <audio
                    ref={audioRef}
                    src={meeting.url_grabacion_publica}
                    onEnded={handleAudioEnded}
                    controls
                    className="w-full"
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {meeting.observaciones_iniciales && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-2 flex items-center space-x-2">
              <DocumentTextIcon className="h-6 w-6" />
              <span>Observaciones Iniciales</span>
            </h2>
            <p className="text-gray-600 whitespace-pre-wrap bg-gray-50 p-4 rounded-lg">{meeting.observaciones_iniciales}</p>
          </div>
        )}

        {meeting.info_adicional && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-2 flex items-center space-x-2">
              <DocumentTextIcon className="h-6 w-6" />
              <span>Información Adicional</span>
            </h2>
            <p className="text-gray-600 whitespace-pre-wrap bg-blue-50 p-4 rounded-lg border border-blue-200">{meeting.info_adicional}</p>
          </div>
        )}

        {meeting.resumen && (
          <div className="mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-2">Resumen</h2>
            <p className="text-gray-600 whitespace-pre-wrap">{meeting.resumen}</p>
          </div>
        )}

        {meeting.puntos_clave && (
          <div className="mb-6">
            <div
              className="flex items-center justify-between cursor-pointer p-3 bg-amber-50 border border-amber-200 rounded-lg hover:bg-amber-100 transition-colors"
              onClick={() => setIsPuntosClaveExpanded(!isPuntosClaveExpanded)}
            >
              <h2 className="text-xl font-semibold text-gray-700 flex items-center space-x-2">
                <KeyIcon className="h-6 w-6 text-amber-600" />
                <span>Puntos Clave</span>
              </h2>
              {isPuntosClaveExpanded ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-500" />
              )}
            </div>

            {isPuntosClaveExpanded && (
              <div className="mt-4 bg-white border border-amber-200 rounded-lg p-4">
                {(() => {
                  let puntosArray: string[] = [];

                  if (typeof meeting.puntos_clave === 'string') {
                    try {
                      const parsed = JSON.parse(meeting.puntos_clave);
                      puntosArray = Array.isArray(parsed) ? parsed : [meeting.puntos_clave];
                    } catch {
                      puntosArray = [meeting.puntos_clave];
                    }
                  } else if (Array.isArray(meeting.puntos_clave)) {
                    puntosArray = meeting.puntos_clave;
                  } else if (meeting.puntos_clave && typeof meeting.puntos_clave === 'object') {
                    puntosArray = Object.values(meeting.puntos_clave).map(String);
                  }

                  return puntosArray.length > 0 ? (
                    <ul className="space-y-3">
                      {puntosArray.map((punto, index) => (
                        <li key={index} className="flex items-start space-x-3">
                          <div className="flex-shrink-0 w-6 h-6 bg-amber-100 text-amber-700 rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                            {index + 1}
                          </div>
                          <p className="text-gray-700 leading-relaxed">{punto}</p>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 italic">No hay puntos clave disponibles.</p>
                  );
                })()}
              </div>
            )}
          </div>
        )}

        {meeting.preguntas_pendientes && (
          <div className="mb-6">
            <div
              className="flex items-center justify-between cursor-pointer p-3 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 transition-colors"
              onClick={() => setIsPreguntasExpanded(!isPreguntasExpanded)}
            >
              <h2 className="text-xl font-semibold text-gray-700 flex items-center space-x-2">
                <QuestionMarkCircleIcon className="h-6 w-6 text-blue-600" />
                <span>Preguntas Pendientes</span>
              </h2>
              {isPreguntasExpanded ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-500" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-500" />
              )}
            </div>

            {isPreguntasExpanded && (
              <div className="mt-4 bg-white border border-blue-200 rounded-lg p-4">
                {(() => {
                  let preguntasArray: PreguntaPendiente[] = [];

                  if (typeof meeting.preguntas_pendientes === 'string') {
                    try {
                      const parsed = JSON.parse(meeting.preguntas_pendientes);
                      preguntasArray = Array.isArray(parsed) ? parsed : [];
                    } catch {
                      preguntasArray = [];
                    }
                  } else if (Array.isArray(meeting.preguntas_pendientes)) {
                    preguntasArray = meeting.preguntas_pendientes;
                  }

                  return preguntasArray.length > 0 ? (
                    <div className="space-y-4">
                      {preguntasArray.map((pregunta, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 w-8 h-8 bg-blue-100 text-blue-700 rounded-full flex items-center justify-center text-sm font-medium">
                              Q{index + 1}
                            </div>
                            <div className="flex-1">
                              <p className="text-gray-800 font-medium mb-2">{pregunta.pregunta_texto}</p>

                              {(pregunta.id_persona || pregunta.id_lead_contacto) && (
                                <div className="mb-2">
                                  <span className="text-sm text-gray-600">
                                    <strong>Dirigida a:</strong> {(() => {
                                      if (pregunta.id_persona) {
                                        // Find the person in the associated entities
                                        const persona = meeting.personas_asociadas?.find(p => p.id === pregunta.id_persona);
                                        if (persona) {
                                          const personaWithApellidos = persona as { id: string; nombre: string; apellidos?: string | null };
                                          const nombreCompleto = `${personaWithApellidos.nombre} ${personaWithApellidos.apellidos || ''}`.trim();
                                          return `${nombreCompleto} (Persona)`;
                                        }
                                        return `Persona ID: ${pregunta.id_persona}`;
                                      }
                                      return '';
                                    })()}
                                  </span>
                                </div>
                              )}

                              {pregunta.info_adicional && (
                                <div className="text-sm text-gray-500 bg-white p-2 rounded border">
                                  <strong>Info adicional:</strong> {pregunta.info_adicional}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">No hay preguntas pendientes.</p>
                  );
                })()}
              </div>
            )}
          </div>
        )}

        {/* Associated Entities Section */}
        {(meeting.empresas_asociadas?.length || meeting.personas_asociadas?.length) && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-xl font-semibold text-gray-700 flex items-center space-x-2">
                <UserGroupIcon className="h-6 w-6" />
                <span>Entidades Asociadas</span>
              </h2>
              <button
                onClick={() => setIsEntitiesExpanded(!isEntitiesExpanded)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {isEntitiesExpanded ? (
                  <ChevronUpIcon className="h-5 w-5" />
                ) : (
                  <ChevronDownIcon className="h-5 w-5" />
                )}
              </button>
            </div>

            {isEntitiesExpanded && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {meeting.empresas_asociadas && meeting.empresas_asociadas.length > 0 && (
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <h3 className="font-semibold text-blue-800 mb-2 flex items-center space-x-2">
                      <BuildingOfficeIcon className="h-5 w-5" />
                      <span>Empresas ({meeting.empresas_asociadas.length})</span>
                    </h3>
                    <ul className="space-y-1">
                      {meeting.empresas_asociadas.map((empresa) => (
                        <li key={empresa.id} className="text-blue-700 text-sm">
                          • {empresa.nombre}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {meeting.personas_asociadas && meeting.personas_asociadas.length > 0 && (
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <h3 className="font-semibold text-green-800 mb-2 flex items-center space-x-2">
                      <UserGroupIcon className="h-5 w-5" />
                      <span>Personas ({meeting.personas_asociadas.length})</span>
                    </h3>
                    <ul className="space-y-1">
                      {meeting.personas_asociadas.map((persona) => (
                        <li key={persona.id} className="text-green-700 text-sm">
                          • {persona.nombre}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}


              </div>
            )}
          </div>
        )}

        {/* Speaker Assignments Section */}
        {meeting.speaker_asignaciones && meeting.speaker_asignaciones.length > 0 && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <h2 className="text-xl font-semibold text-gray-700 flex items-center space-x-2">
                <SpeakerWaveIcon className="h-6 w-6" />
                <span>Asignaciones de Speakers</span>
              </h2>
              <button
                onClick={() => setIsSpeakersExpanded(!isSpeakersExpanded)}
                className="text-gray-500 hover:text-gray-700 transition-colors"
              >
                {isSpeakersExpanded ? (
                  <ChevronUpIcon className="h-5 w-5" />
                ) : (
                  <ChevronDownIcon className="h-5 w-5" />
                )}
              </button>
            </div>

            {isSpeakersExpanded && (
              <div className="bg-gray-50 p-4 rounded-lg border">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {meeting.speaker_asignaciones.map((assignment, index) => (
                    <div key={index} className="bg-white p-3 rounded border border-gray-200">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-gray-800">{assignment.speaker_tag}</span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {assignment.tipo_entidad}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{assignment.nombre_asignado}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Transcription Section */}
        {(meeting.transcripcion_final || meeting.transcripcion_raw) && (
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <DocumentTextIcon className="h-6 w-6 text-gray-700 mr-2" />
              <h2 className="text-xl font-semibold text-gray-700">
                {meeting.transcripcion_final ? 'Transcripción Final' : 'Transcripción Cruda'}
                {meeting.transcripcion_final && (
                  <CheckCircleIcon className="h-5 w-5 text-green-600 inline ml-2" />
                )}
              </h2>
            </div>

            <TranscriptViewer
              transcript={meeting.transcripcion_final || meeting.transcripcion_raw || ''}
              title={meeting.transcripcion_final ? 'Transcripción Final con Speakers Asignados' : 'Transcripción Cruda'}
              isExpanded={isTranscriptExpanded}
              onToggleExpanded={setIsTranscriptExpanded}
              maxPreviewSegments={3}
            />
          </div>
        )}

        </div>
      </div>
    </div>
  );
};

export default MeetingDetailsView;